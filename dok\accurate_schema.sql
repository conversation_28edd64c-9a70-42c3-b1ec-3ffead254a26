
-- Tabel Customers
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VA<PERSON>HAR(50) NOT NULL,
    name VA<PERSON>HAR(100) NOT NULL,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    npwp VARCHAR(30),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Tabel Sales Orders
CREATE TABLE sales_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(50) NOT NULL,
    customer_id INT,
    order_date DATE NOT NULL,
    due_date DATE,
    status ENUM('draft','approved','delivered','invoiced') DEFAULT 'draft',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id)
);

-- Tabel Sales Order Items
CREATE TABLE sales_order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sales_order_id INT,
    product_id INT,
    quantity INT NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount DECIMAL(15,2) DEFAULT 0,
    subtotal DECIMAL(15,2) AS (quantity * unit_price - discount) STORED,
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Tabel Delivery Orders
CREATE TABLE delivery_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_no VARCHAR(50),
    sales_order_id INT,
    delivery_date DATE,
    status ENUM('draft','sent','completed') DEFAULT 'draft',
    notes TEXT,
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id)
);

-- Tabel Delivery Items
CREATE TABLE delivery_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    delivery_order_id INT,
    product_id INT,
    quantity INT NOT NULL,
    FOREIGN KEY (delivery_order_id) REFERENCES delivery_orders(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Tabel Invoices
CREATE TABLE invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_no VARCHAR(50),
    customer_id INT,
    sales_order_id INT,
    invoice_date DATE,
    due_date DATE,
    total DECIMAL(15,2),
    status ENUM('unpaid','paid','partial') DEFAULT 'unpaid',
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (sales_order_id) REFERENCES sales_orders(id)
);

-- Tabel Invoice Items
CREATE TABLE invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT,
    product_id INT,
    quantity INT,
    unit_price DECIMAL(15,2),
    discount DECIMAL(15,2),
    FOREIGN KEY (invoice_id) REFERENCES invoices(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Tabel Payments
CREATE TABLE payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT,
    payment_date DATE,
    amount DECIMAL(15,2),
    method ENUM('cash','transfer','giro','credit_card'),
    notes TEXT,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id)
);

-- Tabel Products
CREATE TABLE products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE,
    name VARCHAR(100),
    unit VARCHAR(20),
    price DECIMAL(15,2),
    stock INT DEFAULT 0
);

-- Tabel Warehouses
CREATE TABLE warehouses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    location TEXT
);

-- Tabel Stock Movements
CREATE TABLE stock_movements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id INT,
    warehouse_id INT,
    movement_date DATE,
    type ENUM('in','out','adjustment'),
    reference VARCHAR(100),
    quantity INT,
    notes TEXT,
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (warehouse_id) REFERENCES warehouses(id)
);

-- Tabel Suppliers
CREATE TABLE suppliers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100),
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    npwp VARCHAR(30)
);

-- Tabel Purchase Orders
CREATE TABLE purchase_orders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    po_no VARCHAR(50) NOT NULL,
    supplier_id INT,
    order_date DATE NOT NULL,
    due_date DATE,
    status ENUM('draft','ordered','received','invoiced') DEFAULT 'draft',
    priority ENUM('low','normal','high','urgent') DEFAULT 'normal',
    notes TEXT,
    discount_type ENUM('percentage','fixed') DEFAULT 'percentage',
    discount_value DECIMAL(15,2) DEFAULT 0,
    tax_percentage DECIMAL(5,2) DEFAULT 0,
    shipping_cost DECIMAL(15,2) DEFAULT 0,
    total_amount DECIMAL(15,2) DEFAULT 0,
    created_by INT,
    updated_by INT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id)
);

-- Tabel Purchase Order Items
CREATE TABLE purchase_order_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    purchase_order_id INT,
    product_id INT,
    quantity INT NOT NULL,
    unit_price DECIMAL(15,2) NOT NULL,
    discount DECIMAL(15,2) DEFAULT 0,
    subtotal DECIMAL(15,2) AS (quantity * unit_price - discount) STORED,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id)
);

-- Sample Data untuk Purchase Orders
INSERT INTO purchase_orders (po_no, supplier_id, order_date, due_date, status, priority, notes, total_amount, created_by) VALUES
('PO-2025-001', 2, '2025-01-15', '2025-01-30', 'draft', 'normal', 'Purchase order untuk stok bulanan', 1500000, 1),
('PO-2025-002', 2, '2025-01-16', '2025-02-01', 'ordered', 'high', 'Purchase order urgent untuk proyek khusus', 2750000, 1),
('PO-2025-003', 2, '2025-01-17', '2025-02-15', 'received', 'normal', 'Purchase order untuk restok barang laris', 980000, 1);

-- Sample Data untuk Purchase Order Items
INSERT INTO purchase_order_items (purchase_order_id, product_id, quantity, unit_price, discount, notes) VALUES
(1, 1, 50, 8000, 0, 'Barang untuk stok reguler'),
(1, 4, 20, 90000, 50000, 'Diskon khusus supplier'),
(2, 1, 100, 8000, 0, 'Order dalam jumlah besar'),
(2, 4, 30, 90000, 0, 'Untuk proyek khusus'),
(3, 1, 25, 8000, 0, 'Restok barang laris'),
(3, 4, 15, 90000, 25000, 'Diskon volume');

-- Tabel Purchase Invoices
CREATE TABLE purchase_invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_no VARCHAR(50),
    supplier_id INT,
    po_id INT,
    invoice_date DATE,
    total DECIMAL(15,2),
    status ENUM('unpaid','paid','partial'),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (po_id) REFERENCES purchase_orders(id)
);

-- Tabel Accounts
CREATE TABLE accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(20) UNIQUE,
    name VARCHAR(100),
    type ENUM('asset','liability','equity','revenue','expense')
);

-- Tabel Journal Entries
CREATE TABLE journal_entries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    entry_date DATE,
    description TEXT,
    reference VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Tabel Journal Details
CREATE TABLE journal_details (
    id INT AUTO_INCREMENT PRIMARY KEY,
    journal_entry_id INT,
    account_id INT,
    debit DECIMAL(15,2) DEFAULT 0,
    credit DECIMAL(15,2) DEFAULT 0,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id),
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);
